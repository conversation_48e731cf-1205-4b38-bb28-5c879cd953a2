# PropBolt Enterprise - App Engine Dispatch Configuration
# Routes domains to auto-scaling services with load balancing
# Priority: API (Golang) > API1 (Python) > Landing

dispatch:
  # HIGHEST PRIORITY: API2 Service (api2.propbolt.com)
  # Golang property data and zestimate API
  - url: "api2.propbolt.com/*"
    service: api2

  # HIGH PRIORITY: API1 Service (api1.propbolt.com)
  # Python Real Estate API with 13 endpoints
  - url: "api1.propbolt.com/*"
    service: api1

  # LOW PRIORITY: Landing Page (propbolt.com)
  # Marketing site with basic scaling
  - url: "propbolt.com/*"
    service: default

  # WWW redirect to main domain
  - url: "www.propbolt.com/*"
    service: default

  # Default fallback for any unmatched domains
  - url: "*/*"
    service: default

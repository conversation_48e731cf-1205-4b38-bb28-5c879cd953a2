#!/bin/bash

# PropBolt API2 Proxy Testing Script
# Tests ProxyMesh residential proxy configuration for Zillow scraping

set -e

echo "🔧 PropBolt API2 Proxy Testing"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -d "api2" ]; then
    echo -e "${RED}Error: api2 directory not found. Run this script from the project root.${NC}"
    exit 1
fi

echo -e "${YELLOW}Step 1: Checking API2 dependencies...${NC}"
cd api2

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo -e "${RED}Error: Go is not installed. Please install Go 1.21+${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Go is installed: $(go version)${NC}"

# Check go.mod
if [ ! -f "go.mod" ]; then
    echo -e "${RED}Error: go.mod not found in api2 directory${NC}"
    exit 1
fi

echo -e "${YELLOW}Step 2: Installing dependencies...${NC}"
go mod tidy

echo -e "${YELLOW}Step 3: Building API2...${NC}"
go build -o propbolt-api2

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ API2 built successfully${NC}"
else
    echo -e "${RED}❌ API2 build failed${NC}"
    exit 1
fi

echo -e "${YELLOW}Step 4: Starting API2 server...${NC}"
# Start server in background
PORT=8080 ./propbolt-api2 &
SERVER_PID=$!

# Wait for server to start
sleep 3

# Function to cleanup on exit
cleanup() {
    echo -e "${YELLOW}Cleaning up...${NC}"
    kill $SERVER_PID 2>/dev/null || true
    rm -f propbolt-api2
}
trap cleanup EXIT

echo -e "${YELLOW}Step 5: Testing API2 endpoints...${NC}"

# Test health check
echo -e "${YELLOW}Testing health check...${NC}"
if curl -s http://localhost:8080/ | grep -q "ok"; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed${NC}"
    exit 1
fi

# Test proxy configuration
echo -e "${YELLOW}Testing proxy configuration...${NC}"
PROXY_RESPONSE=$(curl -s http://localhost:8080/proxy-test)
echo "Proxy test response: $PROXY_RESPONSE"

if echo "$PROXY_RESPONSE" | grep -q "proxy_enabled.*true"; then
    echo -e "${GREEN}✅ Proxy is enabled${NC}"
else
    echo -e "${RED}❌ Proxy is not enabled${NC}"
fi

if echo "$PROXY_RESPONSE" | grep -q "us-wa.proxymesh.com"; then
    echo -e "${GREEN}✅ ProxyMesh configuration detected${NC}"
else
    echo -e "${YELLOW}⚠️  ProxyMesh configuration not detected${NC}"
fi

# Test property endpoint (basic)
echo -e "${YELLOW}Testing property endpoint...${NC}"
PROPERTY_RESPONSE=$(curl -s "http://localhost:8080/property?address=123%20Main%20St%20New%20York%20NY" || echo "FAILED")

if [ "$PROPERTY_RESPONSE" != "FAILED" ] && [ ! -z "$PROPERTY_RESPONSE" ]; then
    echo -e "${GREEN}✅ Property endpoint responded${NC}"
    echo "Response length: $(echo "$PROPERTY_RESPONSE" | wc -c) characters"
else
    echo -e "${RED}❌ Property endpoint failed${NC}"
fi

echo -e "${YELLOW}Step 6: Testing Zillow connectivity...${NC}"
# This would test actual Zillow scraping - be careful with rate limits
echo -e "${YELLOW}⚠️  Zillow connectivity test skipped (to avoid rate limits)${NC}"
echo -e "${YELLOW}   To test manually: curl 'http://localhost:8080/property?id=123456789'${NC}"

echo ""
echo -e "${GREEN}🎉 API2 Proxy Testing Complete!${NC}"
echo ""
echo -e "${YELLOW}Summary:${NC}"
echo "- API2 server: ✅ Running on port 8080"
echo "- Health check: ✅ Passed"
echo "- Proxy config: Check output above"
echo "- Property endpoint: Check output above"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Verify proxy connection is working"
echo "2. Test with real Zillow property IDs"
echo "3. Monitor for rate limiting issues"
echo "4. Check data extraction accuracy"
echo ""
echo -e "${YELLOW}Manual Testing Commands:${NC}"
echo "curl http://localhost:8080/proxy-test"
echo "curl 'http://localhost:8080/property?address=123%20Main%20St%20New%20York%20NY'"
echo "curl 'http://localhost:8080/property?id=123456789'"

# PropBolt - Project Structure (Development Phase)

## Overview
This document outlines the PropBolt project structure during the assessment and redevelopment phase. **Priority: API2 residential proxy configuration for Zillow API calls.**

## Current Development Status
🚧 **UNDER ASSESSMENT & REDEVELOPMENT** - Not production ready

### Existing Services (Assessment Phase)
- **api1.propbolt.com** - Python/Flask with Google Cloud Endpoints (Under Review)
- **api2.propbolt.com** - Go-based Zillow scraper (**PRIORITY** - Needs ProxyMesh residential proxy)
- **data.propbolt.com** - Python/Flask with 13 endpoints (Legacy - Under Review)
- **go.propbolt.com** - Next.js dashboard (Under Review)
- **propbolt.com** - Next.js landing page (Under Review)

### Current Priority Issues
1. **API2 Residential Proxy** - ProxyMesh configuration for Zillow scraping
2. Scattered configuration files
3. Multiple deployment scripts
4. Inconsistent proxy configurations
5. Complex folder structure

## Proposed New Structure

```
propbolt/
├── README.md
├── PROJECT_STRUCTURE.md
├── .env.example
├── docker-compose.yml
├── deploy.sh
│
├── services/
│   ├── api1/                    # Python API (api1.propbolt.com)
│   │   ├── main.py
│   │   ├── requirements.txt
│   │   ├── app.yaml
│   │   └── database/
│   │
│   ├── api2/                    # Zillow Scraper (api2.propbolt.com)
│   │   ├── main.go
│   │   ├── go.mod
│   │   ├── app.yaml
│   │   ├── proxy/
│   │   └── modules/
│   │
│   ├── data/                    # Real Estate API (data.propbolt.com)
│   │   ├── main.py
│   │   ├── requirements.txt
│   │   ├── app.yaml
│   │   └── endpoints/
│   │
│   ├── dashboard/               # User Dashboard (go.propbolt.com)
│   │   ├── package.json
│   │   ├── next.config.js
│   │   ├── app.yaml
│   │   └── src/
│   │
│   └── landing/                 # Landing Page (propbolt.com)
│       ├── package.json
│       ├── next.config.js
│       ├── app.yaml
│       └── src/
│
├── shared/
│   ├── config/
│   │   ├── proxy.yaml
│   │   ├── database.yaml
│   │   └── auth.yaml
│   │
│   ├── utils/
│   │   ├── auth.py
│   │   ├── database.py
│   │   └── proxy.js
│   │
│   └── schemas/
│       ├── api_keys.sql
│       ├── users.sql
│       └── properties.sql
│
├── infrastructure/
│   ├── gcloud/
│   │   ├── setup.sh
│   │   ├── deploy-all.sh
│   │   └── dns-config.sh
│   │
│   ├── database/
│   │   ├── init.sql
│   │   ├── migrations/
│   │   └── backup.sh
│   │
│   └── monitoring/
│       ├── health-checks.yaml
│       └── alerts.yaml
│
├── docs/
│   ├── API_DOCUMENTATION.md
│   ├── DEPLOYMENT.md
│   ├── PROXY_CONFIGURATION.md
│   └── DATABASE_SCHEMA.md
│
└── scripts/
    ├── setup-dev.sh
    ├── test-all.sh
    ├── backup.sh
    └── cleanup.sh
```

## Key Improvements

### 1. Centralized Configuration
- Single `.env` file for all services
- Shared configuration in `shared/config/`
- Consistent proxy settings across services

### 2. Simplified Deployment
- Single `deploy.sh` script for all services
- Unified Google Cloud configuration
- Automated DNS setup

### 3. Better Organization
- Services clearly separated by domain
- Shared utilities to avoid duplication
- Infrastructure code isolated

### 4. Enhanced Maintainability
- Clear documentation structure
- Standardized naming conventions
- Consistent file organization

## Development Plan (Priority: API2)

### Phase 1: API2 Proxy Configuration (CURRENT PRIORITY)
1. **Verify ProxyMesh Configuration** - Ensure API2 can connect to Zillow
2. **Test Residential Proxy** - Validate us-wa.proxymesh.com connectivity
3. **Document Proxy Setup** - Create clear proxy configuration guide
4. **Local Development Setup** - Enable local testing with proxy

### Phase 2: API2 Functionality Assessment
1. Test all Zillow scraping endpoints
2. Validate data extraction accuracy
3. Performance testing with proxy
4. Error handling and retry logic

### Phase 3: Other Services Assessment
1. Review api1 functionality and requirements
2. Assess dashboard and landing page needs
3. Evaluate data service endpoints
4. Determine what to keep vs rebuild

### Phase 4: Structure Consolidation (After API2 is stable)
1. Create unified deployment scripts
2. Standardize configurations
3. Update documentation

## Benefits

1. **Easier Development**: Clear separation of concerns
2. **Simplified Deployment**: Single command deployment
3. **Better Maintenance**: Centralized configuration
4. **Improved Scaling**: Modular architecture
5. **Enhanced Security**: Consistent authentication

## Next Steps

1. Create the new directory structure
2. Begin migrating services one by one
3. Test each service after migration
4. Update deployment scripts
5. Document the new structure

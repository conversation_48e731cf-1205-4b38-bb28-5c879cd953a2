# PropBolt - New Project Structure

## Overview
This document outlines the new, simplified project structure for PropBolt, consolidating all services into a clean, maintainable architecture.

## Current Structure Analysis

### Existing Services
- **api1.propbolt.com** - Python/Flask with Google Cloud Endpoints
- **api2.propbolt.com** - Python-based Zillow scraper (Go implementation)
- **data.propbolt.com** - Python/Flask with 13 endpoints for real estate data API
- **go.propbolt.com** - Next.js dashboard for users
- **propbolt.com** - Next.js landing page

### Current Issues
- Scattered configuration files
- Multiple deployment scripts
- Inconsistent proxy configurations
- Complex folder structure

## Proposed New Structure

```
propbolt/
├── README.md
├── PROJECT_STRUCTURE.md
├── .env.example
├── docker-compose.yml
├── deploy.sh
│
├── services/
│   ├── api1/                    # Python API (api1.propbolt.com)
│   │   ├── main.py
│   │   ├── requirements.txt
│   │   ├── app.yaml
│   │   └── database/
│   │
│   ├── api2/                    # Zillow Scraper (api2.propbolt.com)
│   │   ├── main.go
│   │   ├── go.mod
│   │   ├── app.yaml
│   │   ├── proxy/
│   │   └── modules/
│   │
│   ├── data/                    # Real Estate API (data.propbolt.com)
│   │   ├── main.py
│   │   ├── requirements.txt
│   │   ├── app.yaml
│   │   └── endpoints/
│   │
│   ├── dashboard/               # User Dashboard (go.propbolt.com)
│   │   ├── package.json
│   │   ├── next.config.js
│   │   ├── app.yaml
│   │   └── src/
│   │
│   └── landing/                 # Landing Page (propbolt.com)
│       ├── package.json
│       ├── next.config.js
│       ├── app.yaml
│       └── src/
│
├── shared/
│   ├── config/
│   │   ├── proxy.yaml
│   │   ├── database.yaml
│   │   └── auth.yaml
│   │
│   ├── utils/
│   │   ├── auth.py
│   │   ├── database.py
│   │   └── proxy.js
│   │
│   └── schemas/
│       ├── api_keys.sql
│       ├── users.sql
│       └── properties.sql
│
├── infrastructure/
│   ├── gcloud/
│   │   ├── setup.sh
│   │   ├── deploy-all.sh
│   │   └── dns-config.sh
│   │
│   ├── database/
│   │   ├── init.sql
│   │   ├── migrations/
│   │   └── backup.sh
│   │
│   └── monitoring/
│       ├── health-checks.yaml
│       └── alerts.yaml
│
├── docs/
│   ├── API_DOCUMENTATION.md
│   ├── DEPLOYMENT.md
│   ├── PROXY_CONFIGURATION.md
│   └── DATABASE_SCHEMA.md
│
└── scripts/
    ├── setup-dev.sh
    ├── test-all.sh
    ├── backup.sh
    └── cleanup.sh
```

## Key Improvements

### 1. Centralized Configuration
- Single `.env` file for all services
- Shared configuration in `shared/config/`
- Consistent proxy settings across services

### 2. Simplified Deployment
- Single `deploy.sh` script for all services
- Unified Google Cloud configuration
- Automated DNS setup

### 3. Better Organization
- Services clearly separated by domain
- Shared utilities to avoid duplication
- Infrastructure code isolated

### 4. Enhanced Maintainability
- Clear documentation structure
- Standardized naming conventions
- Consistent file organization

## Migration Plan

### Phase 1: Structure Creation
1. Create new directory structure
2. Move existing services to new locations
3. Update import paths and references

### Phase 2: Configuration Consolidation
1. Merge environment variables
2. Standardize proxy configuration
3. Unify database connections

### Phase 3: Deployment Simplification
1. Create unified deployment scripts
2. Update Google Cloud configurations
3. Test all services

### Phase 4: Documentation Update
1. Update all README files
2. Create comprehensive API documentation
3. Document deployment procedures

## Benefits

1. **Easier Development**: Clear separation of concerns
2. **Simplified Deployment**: Single command deployment
3. **Better Maintenance**: Centralized configuration
4. **Improved Scaling**: Modular architecture
5. **Enhanced Security**: Consistent authentication

## Next Steps

1. Create the new directory structure
2. Begin migrating services one by one
3. Test each service after migration
4. Update deployment scripts
5. Document the new structure

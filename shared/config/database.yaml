# PropBolt Database Configuration
# Centralized database settings for all services

database:
  # Primary PostgreSQL instance
  primary:
    type: "postgresql"
    host: "${DB_HOST}"
    port: 5432
    name: "${DB_NAME}"
    user: "${DB_USER}"
    password: "${DB_PASSWORD}"
    ssl_mode: "require"
    
  # Connection pool settings
  pool:
    min_connections: 5
    max_connections: 20
    connection_timeout: 30
    idle_timeout: 300
    
  # Service-specific database configurations
  services:
    api1:
      database: "propbolt_api1"
      tables:
        - "api_keys"
        - "api_usage"
        - "daily_usage_summary"
        
    api2:
      database: "propbolt_api2"
      tables:
        - "scraping_logs"
        - "proxy_usage"
        
    data:
      database: "propbolt_data"
      tables:
        - "properties"
        - "market_data"
        - "api_requests"
        
    dashboard:
      database: "propbolt_users"
      tables:
        - "users"
        - "sessions"
        - "user_preferences"
        
    landing:
      database: "propbolt_marketing"
      tables:
        - "leads"
        - "analytics"

# Backup configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention_days: 30
  storage_bucket: "${BACKUP_BUCKET}"
  
# Migration settings
migrations:
  auto_migrate: false
  migration_path: "./infrastructure/database/migrations"
  
# Monitoring
monitoring:
  slow_query_threshold: 1000  # milliseconds
  connection_monitoring: true
  performance_insights: true

# PropBolt Proxy Configuration
# Centralized proxy settings for all services

proxy:
  # Primary proxy service (ProxyMesh)
  primary:
    enabled: true
    provider: "proxymesh"
    host: "us-wa.proxymesh.com"
    port: 31280
    username: "propbolt_user"
    password: "proxy_pass_2024"
    timeout: 30
    rotation: true
    
  # Backup proxy service (ProxyRack)
  backup:
    enabled: false
    provider: "proxyrack"
    host: "premium-residential.proxyrack.net"
    port: 9000
    username: ""
    password: ""
    timeout: 30
    
  # Google Cloud proxy integration
  gcloud:
    enabled: false
    endpoint: ""
    service_account: ""
    
  # Service-specific overrides
  services:
    api1:
      use_proxy: false
      
    api2:
      use_proxy: true
      provider: "primary"
      
    data:
      use_proxy: true
      provider: "primary"
      
    dashboard:
      use_proxy: false
      
    landing:
      use_proxy: false

# Health check settings
health_check:
  enabled: true
  interval: 300  # 5 minutes
  timeout: 10
  test_url: "https://httpbin.org/ip"
  
# Logging
logging:
  level: "INFO"
  proxy_requests: true
  failed_requests: true
  rotation_events: true

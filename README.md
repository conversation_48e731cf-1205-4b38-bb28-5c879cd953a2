# PropBolt - Real Estate API Platform

🚧 **DEVELOPMENT PHASE** - Under assessment and redevelopment. Not production ready.

PropBolt is a comprehensive real estate data platform providing property information, market analysis, and investment insights through multiple specialized APIs.

## 🎯 Current Priority: API2 Residential Proxy Configuration

**Focus**: Ensuring API2 can successfully scrape Zillow data using ProxyMesh residential proxies.

## 📁 Project Structure

```
propbolt/
├── api1/                    # Python/Flask API (Under Review)
├── api2/                    # Go Zillow Scraper (PRIORITY - ProxyMesh)
├── dashboard/               # Next.js User Dashboard (Under Review)
├── landing/                 # Next.js Landing Page (Under Review)

```

## 🔧 Services Overview

### API2 - Z<PERSON><PERSON> Scraper (CURRENT PRIORITY)
- **Status**: 🔴 Under Development
- **Technology**: Go
- **Proxy**: ProxyMesh (us-wa.proxymesh.com:31280)
- **Purpose**: Scrape Zillow property data
- **Local Testing**: `cd api2 && go run main.go`

### API1 - Python API (Under Review)
- **Status**: 🟡 Assessment Phase
- **Technology**: Python/Flask
- **Purpose**: Google Cloud Endpoints API
- **Local Testing**: `cd api1 && python main.py`

### Dashboard - User Interface (Under Review)
- **Status**: 🟡 Assessment Phase
- **Technology**: Next.js
- **Purpose**: User dashboard at go.propbolt.com
- **Local Testing**: `cd dashboard && npm run dev`

### Landing - Marketing Site (Under Review)
- **Status**: 🟡 Assessment Phase
- **Technology**: Next.js
- **Purpose**: Landing page at propbolt.com
- **Local Testing**: `cd landing && npm run dev`

## 🔌 API2 Proxy Configuration (PRIORITY)

API2 uses **ProxyMesh** residential proxies to access Zillow data:

```yaml
# Current Configuration (api2/app.yaml)
PROXY_ENABLED: "true"
PROXY_HOST: "us-wa.proxymesh.com"
PROXY_PORT: "31280"
PROXY_USERNAME: "propbolt_user"
PROXY_PASSWORD: "proxy_pass_2024"
PROXY_ROTATION: "true"
PROXY_TIMEOUT: "30"
```

### Testing Proxy Connection
```bash
cd api2
go run main.go
# Test proxy endpoint
curl http://localhost:8080/proxy-test
```

## 🚀 Development Setup

### Prerequisites
- Go 1.21+ (for API2)
- Python 3.9+ (for API1)
- Node.js 18+ (for dashboard/landing)
- Google Cloud SDK

### Quick Start (API2 Priority)
```bash
# Clone repository
git clone <repository-url>
cd propbolt

# Test API2 (Priority)
cd api2
go mod tidy
go run main.go

# Test proxy connection
curl http://localhost:8080/proxy-test
```

## 📋 Development Phases

### Phase 1: API2 Proxy Configuration (CURRENT)
- ✅ Verify ProxyMesh connectivity
- ✅ Test Zillow scraping endpoints
- ⏳ Validate data extraction accuracy
- ⏳ Performance testing with proxy

### Phase 2: API2 Functionality Assessment
- ⏳ Test all endpoints
- ⏳ Error handling validation
- ⏳ Local development setup

### Phase 3: Other Services Assessment
- ⏳ Review API1 requirements
- ⏳ Assess dashboard functionality
- ⏳ Evaluate landing page needs

### Phase 4: Production Readiness
- ⏳ Unified deployment
- ⏳ Documentation completion
- ⏳ Performance optimization

## 📚 Documentation

- [Project Structure Details](NEW_PROJECT_STRUCTURE.md)
- [API2 Documentation](api2/README.md)
- [Proxy Configuration](shared/config/proxy.yaml)

## ⚠️ Important Notes

- **Not Production Ready**: All services under assessment
- **API2 Priority**: Focus on residential proxy configuration
- **Local Testing Only**: Use development environment
- **Proxy Required**: API2 needs ProxyMesh for Zillow access

## 🔗 Service URLs (Development)

- API2 (Local): http://localhost:8080
- API1 (Local): http://localhost:8000
- Dashboard (Local): http://localhost:3000
- Landing (Local): http://localhost:3001

### API2 - Zillow Scraper (CURRENT PRIORITY)
- **Status**: 🔴 Under Development
- **Technology**: Go
- **Proxy**: ProxyMesh (us-wa.proxymesh.com:31280)
- **Purpose**: Scrape Zillow property data
- **Local Testing**: `cd api2 && go run main.go`

## API Endpoints

### Core Services
- `GET /` - API information and endpoint listing
- `GET /health` - Service health status
- `GET /docs` - Interactive API documentation (Swagger UI)

### Property Services
- `POST /v2/PropertySearch` - Search for properties
- `POST /v2/PropertyDetail` - Get detailed property information
- `POST /v2/PropertyDetailBulk` - Bulk property details retrieval
- `POST /v1/PropertyParcel` - Property parcel information

### Comparables & Valuation
- `POST /v2/PropertyComps` - Property comparables (v2)
- `POST /v3/PropertyComps` - Property comparables (v3)
- `POST /v2/PropertyAvm` - Automated Valuation Model

### Address Services
- `POST /v2/AutoComplete` - Address autocomplete
- `POST /v2/AddressVerification` - Address verification

### Advanced Features
- `POST /v2/PropGPT` - AI-powered property insights
- `POST /v2/CSVBuilder` - CSV data builder
- `POST /v2/Reports/PropertyLiens` - Property liens reports
- `POST /v2/PropertyMapping` - Property mapping services

## API Key Management

The API now includes a comprehensive API key management system with the following features:

### Authentication
All real estate API endpoints now require valid API keys:
- **API Key Header**: `Authorization: Bearer pb_live_your_api_key_here`
- **Alternative Header**: `X-API-Key: pb_live_your_api_key_here`
- **Query Parameter**: `?api_key=pb_live_your_api_key_here` (less secure)

### API Key Types
- **Live Keys**: `pb_live_` prefix for production use
- **Test Keys**: `pb_test_` prefix for development/testing

### Tier System
- **Basic**: 60 requests/minute, 1,000 requests/day
- **Premium**: 300 requests/minute, 10,000 requests/day
- **Enterprise**: 1,000 requests/minute, 100,000 requests/day

### Admin Endpoints
- `POST /admin/keys/generate` - Generate new API keys
- `GET /admin/keys` - List all API keys
- `DELETE /admin/keys/{key_id}` - Revoke API keys
- `GET /admin/usage/{key_id}` - Get usage analytics
- `GET /admin/tiers` - Get tier configurations

### Rate Limiting
- Per-minute rate limiting based on API key tier
- Rate limit headers included in responses:
  - `X-RateLimit-Limit`: Maximum requests per minute
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: When the rate limit resets

### Usage Analytics
- Real-time usage tracking and logging
- Daily usage summaries for efficient quota management
- Comprehensive analytics including response times and error rates

## Configuration

Environment variables are configured in `.env` and `app.yaml`:

- `PORT`: Server port (default: 8081)
- `FLASK_ENV`: Flask environment (production)
- `DATABASE_URL`: PostgreSQL connection string
- `REAL_ESTATE_API_KEY`: External API key
- `CORS_ALLOWED_ORIGINS`: Allowed CORS origins

## Database

Uses the same Google Cloud PostgreSQL database as the main API:
- Host: *************
- Database: propbolt
- SSL required

### API Key Management Tables
- `api_keys`: Store API keys with metadata and quotas
- `api_usage`: Log all API requests for analytics
- `daily_usage_summary`: Aggregate daily usage per key
- `rate_limit_windows`: Track rate limiting windows

## Deployment

The service is automatically deployed with the main deployment script:

```bash
./deploy-gcp.sh
```

Or deploy individually:

```bash
cd data
gcloud app deploy app.yaml --quiet --promote
```

## Setup and Installation

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Initialize Database
```bash
# Set environment variables first
export DB_HOST=*************
export DB_NAME=propbolt
export DB_USER=propbolt_user
export DB_PASSWORD=PropBolt2024!

# Initialize API key management tables
python init_api_keys_db.py
```

### 3. Local Development
```bash
# Set environment variables
cp .env.example .env
# Edit .env with your configuration

# Run the server
python main.py
```

The server will start on `http://localhost:8081`

### 4. Generate API Keys
```bash
# Using curl to generate an API key (requires admin token)
curl -X POST http://localhost:8081/admin/keys/generate \
  -H "Content-Type: application/json" \
  -H "X-Admin-Token: admin_token_placeholder" \
  -d '{
    "user_id": "admin-001",
    "name": "My Test Key",
    "tier": "basic",
    "key_type": "test"
  }'
```

### 5. Test API Endpoints
```bash
# Test with API key
curl -X POST http://localhost:8081/v2/PropertySearch \
  -H "Authorization: Bearer pb_test_your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{"address": "123 Main St"}'
```

## Architecture

- **Runtime**: Python 3.9
- **Framework**: Flask with CORS support
- **Database**: PostgreSQL with psycopg2
- **Deployment**: Google Cloud App Engine
- **Auto-scaling**: 1-100 instances based on CPU/throughput
- **Resources**: 2 CPU, 4GB RAM per instance

## Security

- HTTPS enforced
- CORS configured for allowed origins
- Database connections use SSL
- Google Cloud service account authentication

## Monitoring

- Health checks at `/health`
- Structured logging with configurable levels
- Google Cloud monitoring integration

package proxy

import (
	"crypto/tls"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"strconv"
	"time"
)

// ProxyConfig holds proxy configuration
type ProxyConfig struct {
	Enabled  bool
	UseGCloudProxy bool
	Host     string
	Port     string
	Username string
	Password string
	Timeout  time.Duration
	GCloudProxyEndpoint string
}

// LoadProxyConfig loads proxy configuration from environment variables
func LoadProxyConfig() *ProxyConfig {
	enabled, _ := strconv.ParseBool(os.Getenv("PROXY_ENABLED"))
	useGCloudProxy, _ := strconv.ParseBool(os.Getenv("USE_GCLOUD_PROXY"))
	timeoutStr := os.Getenv("PROXY_TIMEOUT")
	timeout := 30 * time.Second
	
	if timeoutStr != "" {
		if t, err := strconv.Atoi(timeoutStr); err == nil {
			timeout = time.Duration(t) * time.Second
		}
	}

	config := &ProxyConfig{
		Enabled:  enabled,
		UseGCloudProxy: useGCloudProxy,
		Host:     os.Getenv("PROXY_HOST"),
		Port:     os.Getenv("PROXY_PORT"),
		Username: os.Getenv("PROXY_USERNAME"),
		Password: os.Getenv("PROXY_PASSWORD"),
		Timeout:  timeout,
		GCloudProxyEndpoint: os.Getenv("GCLOUD_PROXY_ENDPOINT"),
	}

	// If using GCloud proxy, fetch configuration
	if config.UseGCloudProxy && config.Enabled {
		if err := config.fetchGCloudProxyConfig(); err != nil {
			log.Printf("Warning: Failed to fetch GCloud proxy config: %v", err)
		}
	}

	return config
}

// CreateHTTPClient creates an HTTP client with proxy configuration
func (pc *ProxyConfig) CreateHTTPClient() *http.Client {
	client := &http.Client{
		Timeout: pc.Timeout,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: false,
			},
		},
	}

	if pc.Enabled && pc.Host != "" && pc.Port != "" {
		proxyURL := fmt.Sprintf("http://%s:%s@%s:%s", 
			pc.Username, pc.Password, pc.Host, pc.Port)
		
		if parsedURL, err := url.Parse(proxyURL); err == nil {
			client.Transport = &http.Transport{
				Proxy: http.ProxyURL(parsedURL),
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: false,
				},
			}
		}
	}

	return client
}

// GetProxyInfo returns proxy information for logging
func (pc *ProxyConfig) GetProxyInfo() string {
	if !pc.Enabled {
		return "Proxy: Disabled"
	}
	return fmt.Sprintf("Proxy: %s:%s (User: %s)", pc.Host, pc.Port, pc.Username)
}

// TestProxyConnection tests if the proxy is working
func (pc *ProxyConfig) TestProxyConnection() error {
	client := pc.CreateHTTPClient()
	
	// Test with a simple HTTP request
	resp, err := client.Get("https://httpbin.org/ip")
	if err != nil {
		return fmt.Errorf("proxy test failed: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		return fmt.Errorf("proxy test returned status: %d", resp.StatusCode)
	}
	
	return nil
}

// fetchGCloudProxyConfig fetches proxy configuration from Google Cloud
func (pc *ProxyConfig) fetchGCloudProxyConfig() error {
	// This is a placeholder for different Google Cloud proxy solutions
	// The actual implementation will depend on which service you choose:
	// 1. Secure Web Proxy
	// 2. Compute Engine instances as proxies
	// 3. Third-party residential proxy on GCE
	
	if pc.GCloudProxyEndpoint != "" {
		// If endpoint is explicitly set, parse it
		if u, err := url.Parse(pc.GCloudProxyEndpoint); err == nil {
			pc.Host = u.Hostname()
			pc.Port = u.Port()
			if u.User != nil {
				pc.Username = u.User.Username()
				if pass, ok := u.User.Password(); ok {
					pc.Password = pass
				}
			}
			return nil
		}
	}
	
	// Try to fetch from gcloud compute instances
	cmd := exec.Command("gcloud", "compute", "instances", "list", 
		"--filter=labels.proxy-type=residential", 
		"--format=json")
	
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to list proxy instances: %v", err)
	}
	
	// Parse the output and configure proxy
	// This is a simplified example - you'll need to adapt based on your setup
	if len(output) > 0 {
		log.Printf("Found GCloud proxy instances: %s", string(output))
		// Parse JSON and extract proxy details
		// Set pc.Host, pc.Port, etc.
	}
	
	return nil
}

// GetProxyURL returns the proxy URL for use in HTTP clients
func (pc *ProxyConfig) GetProxyURL() *url.URL {
	if !pc.Enabled || pc.Host == "" || pc.Port == "" {
		return nil
	}
	
	proxyURL := fmt.Sprintf("http://%s:%s", pc.Host, pc.Port)
	if pc.Username != "" && pc.Password != "" {
		proxyURL = fmt.Sprintf("http://%s:%s@%s:%s", 
			pc.Username, pc.Password, pc.Host, pc.Port)
	}
	
	parsedURL, err := url.Parse(proxyURL)
	if err != nil {
		log.Printf("Failed to parse proxy URL: %v", err)
		return nil
	}
	
	return parsedURL
}

openapi: 3.0.3
info:
  title: PropBolt API (Zillow Data Service)
  description: |
    PropBolt's real estate data API that provides property details, rent estimates, and property search functionality.
    The API scrapes and processes data from Zillow to provide comprehensive property information.
  version: 2.0.0
  contact:
    email: <EMAIL>
servers:
  - url: http://localhost:8080
    description: Local development server
  - url: https://api2.propbolt.com
    description: Production server
paths:
  /:
    get:
      tags:
        - Health
      summary: Health Check
      description: Returns the health status of the API
      operationId: healthCheck
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
  /property:
    get:
      tags:
        - Property Details
      summary: Get Full Property Details
      description: Retrieves comprehensive property information by ID, URL, or address
      operationId: getPropertyFull
      parameters:
        - name: id
          in: query
          description: Zillow Property ID (zpid)
          schema:
            type: integer
            format: int64
            example: 47563607
        - name: url
          in: query
          description: Zillow property URL
          schema:
            type: string
            example: https://www.zillow.com/homedetails/123-main-st/123456789_zpid/
        - name: address
          in: query
          description: Property address
          schema:
            type: string
            example: 320 alba st e
        - name: listingPhotos
          in: query
          description: Include listing photos
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Successful response with property details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyInfo'
        '400':
          description: Bad request - must provide ID, URL, or Address parameter
          content:
            text/plain:
              schema:
                type: string
                example: Must provide ID, URL, or Address parameter
        '500':
          description: Internal server error
          content:
            text/plain:
              schema:
                type: string
                example: Error retrieving property details
  /propertyMinimal:
    get:
      tags:
        - Property Details
      summary: Get Minimal Property Details
      description: Retrieves essential property information with reduced data payload
      operationId: getPropertyMinimal
      parameters:
        - name: id
          in: query
          description: Zillow Property ID (zpid)
          schema:
            type: integer
            format: int64
        - name: url
          in: query
          description: Zillow property URL
          schema:
            type: string
        - name: address
          in: query
          description: Property address
          schema:
            type: string
        - name: listingPhotos
          in: query
          description: Include listing photos
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Successful response with minimal property details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyMinimalInfo'
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/plain:
              schema:
                type: string
  /propertyImages:
    get:
      tags:
        - Property Details
      summary: Get Property Images Only
      description: Retrieves only the property images and basic identification information
      operationId: getPropertyImages
      parameters:
        - name: id
          in: query
          description: Zillow Property ID (zpid)
          schema:
            type: integer
            format: int64
        - name: url
          in: query
          description: Zillow property URL
          schema:
            type: string
        - name: address
          in: query
          description: Property address
          schema:
            type: string
      responses:
        '200':
          description: Successful response with property images
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImagesOnly'
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/plain:
              schema:
                type: string
  /rentEstimate:
    get:
      tags:
        - Rent Estimate
      summary: Get Rent Estimate
      description: Retrieves rental market analysis and rent estimates for a given address
      operationId: getRentEstimate
      parameters:
        - name: address
          in: query
          description: Property address
          required: true
          schema:
            type: string
            example: 320 alba st e
        - name: compPropStatus
          in: query
          description: Include comparable properties status
          schema:
            type: boolean
        - name: distanceInMiles
          in: query
          description: Search radius for comparables
          schema:
            type: number
            format: float
            default: 5.0
            minimum: 0.1
            maximum: 50.0
      responses:
        '200':
          description: Successful response with rent estimate
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RentZestimateResponse'
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/plain:
              schema:
                type: string
  /search/for-sale:
    get:
      tags:
        - Property Search
      summary: Search Properties For Sale
      description: Search for properties currently for sale within specified geographic bounds
      operationId: searchForSale
      parameters:
        - $ref: '#/components/parameters/neLat'
        - $ref: '#/components/parameters/neLong'
        - $ref: '#/components/parameters/swLat'
        - $ref: '#/components/parameters/swLong'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/zoom'
        - name: isAllHomes
          in: query
          description: Include all home types
          schema:
            type: boolean
            default: true
        - name: isTownhouse
          in: query
          description: Include townhouses
          schema:
            type: boolean
            default: false
        - name: isMultiFamily
          in: query
          description: Include multi-family properties
          schema:
            type: boolean
            default: false
        - name: isCondo
          in: query
          description: Include condos
          schema:
            type: boolean
            default: false
        - name: isLotLand
          in: query
          description: Include lots/land
          schema:
            type: boolean
            default: false
        - name: isApartment
          in: query
          description: Include apartments
          schema:
            type: boolean
            default: false
        - name: isManufactured
          in: query
          description: Include manufactured homes
          schema:
            type: boolean
            default: false
        - name: isApartmentOrCondo
          in: query
          description: Include apartments or condos
          schema:
            type: boolean
            default: false
        - name: isElementarySchool
          in: query
          description: Filter by elementary school
          schema:
            type: boolean
            default: false
        - name: isMiddleSchool
          in: query
          description: Filter by middle school
          schema:
            type: boolean
            default: false
        - name: isHighSchool
          in: query
          description: Filter by high school
          schema:
            type: boolean
            default: false
        - name: isPublicSchool
          in: query
          description: Filter by public schools
          schema:
            type: boolean
            default: false
        - name: isPrivateSchool
          in: query
          description: Filter by private schools
          schema:
            type: boolean
            default: false
        - name: isCharterSchool
          in: query
          description: Filter by charter schools
          schema:
            type: boolean
            default: false
        - name: includeUnratedSchools
          in: query
          description: Include unrated schools
          schema:
            type: boolean
            default: false
        - name: priceMin
          in: query
          description: Minimum price
          schema:
            type: integer
            default: 0
            minimum: 0
        - name: priceMax
          in: query
          description: Maximum price
          schema:
            type: integer
            default: 0
            minimum: 0
        - name: monthlyPaymentMin
          in: query
          description: Minimum monthly payment
          schema:
            type: integer
            default: 0
            minimum: 0
        - name: monthlyPaymentMax
          in: query
          description: Maximum monthly payment
          schema:
            type: integer
            default: 0
            minimum: 0
      responses:
        '200':
          description: Successful response with search results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponse'
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/plain:
              schema:
                type: string
  /search/for-rent:
    get:
      tags:
        - Property Search
      summary: Search Rental Properties
      description: Search for rental properties within specified geographic bounds
      operationId: searchForRent
      parameters:
        - $ref: '#/components/parameters/neLat'
        - $ref: '#/components/parameters/neLong'
        - $ref: '#/components/parameters/swLat'
        - $ref: '#/components/parameters/swLong'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/zoom'
      responses:
        '200':
          description: Successful response with search results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponse'
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/plain:
              schema:
                type: string
  /search/sold:
    get:
      tags:
        - Property Search
      summary: Search Recently Sold Properties
      description: Search for recently sold properties within specified geographic bounds
      operationId: searchSold
      parameters:
        - $ref: '#/components/parameters/neLat'
        - $ref: '#/components/parameters/neLong'
        - $ref: '#/components/parameters/swLat'
        - $ref: '#/components/parameters/swLong'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/zoom'
      responses:
        '200':
          description: Successful response with search results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponse'
        '400':
          description: Bad request
          content:
            text/plain:
              schema:
                type: string
        '500':
          description: Internal server error
          content:
            text/plain:
              schema:
                type: string
components:
  parameters:
    neLat:
      name: neLat
      in: query
      description: Northeast latitude boundary
      required: true
      schema:
        type: number
        format: double
        example: 37.8
    neLong:
      name: neLong
      in: query
      description: Northeast longitude boundary
      required: true
      schema:
        type: number
        format: double
        example: -122.3
    swLat:
      name: swLat
      in: query
      description: Southwest latitude boundary
      required: true
      schema:
        type: number
        format: double
        example: 37.7
    swLong:
      name: swLong
      in: query
      description: Southwest longitude boundary
      required: true
      schema:
        type: number
        format: double
        example: -122.5
    page:
      name: page
      in: query
      description: Page number for pagination
      schema:
        type: integer
        default: 1
        minimum: 1
    zoom:
      name: zoom
      in: query
      description: Map zoom level
      schema:
        type: integer
        default: 10
        minimum: 1
        maximum: 20
  schemas:
    StringOrArray:
      description: Field that can be either a string or an array of strings
      oneOf:
        - type: string
        - type: array
          items:
            type: string
    Address:
      type: object
      properties:
        streetAddress:
          $ref: '#/components/schemas/StringOrArray'
        city:
          $ref: '#/components/schemas/StringOrArray'
        state:
          $ref: '#/components/schemas/StringOrArray'
        zipcode:
          $ref: '#/components/schemas/StringOrArray'
    ResponsivePhoto:
      type: object
      properties:
        url:
          type: string
        width:
          type: integer
        height:
          type: integer
    PropertyInfo:
      type: object
      properties:
        homeStatus:
          $ref: '#/components/schemas/StringOrArray'
        address:
          $ref: '#/components/schemas/Address'
        bedrooms:
          type: integer
        bathrooms:
          type: number
        yearBuilt:
          type: integer
        currency:
          type: string
        cityId:
          type: integer
        timeOnZillow:
          type: string
          nullable: true
        zestimate:
          type: integer
        price:
          type: integer
        countyId:
          type: integer
        stateId:
          type: integer
        zpid:
          type: integer
        country:
          type: string
        hdpUrl:
          type: string
        propertyTaxRate:
          type: number
        latitude:
          type: number
        longitude:
          type: number
        rentZestimate:
          type: integer
        zestimateLowPercent:
          type: string
        zestimateHighPercent:
          type: string
        lastSoldPrice:
          type: integer
        annualHomeownersInsurance:
          type: integer
        daysOnZillow:
          type: integer
        favoriteCount:
          type: integer
        monthlyHoaFee:
          type: integer
        lotSize:
          type: integer
        lotAreaValue:
          type: number
        lotAreaUnits:
          type: string
        pageViewCount:
          type: integer
        parcelId:
          type: string
        brokerageName:
          type: string
        description:
          $ref: '#/components/schemas/StringOrArray'
        livingAreaUnitsShort:
          type: string
        virtualTourUrl:
          type: string
        datePostedString:
          type: string
          nullable: true
        propertyTypeDimension:
          type: string
        isZillowOwned:
          type: boolean
        responsivePhotos:
          type: array
          items:
            $ref: '#/components/schemas/ResponsivePhoto'
        resoFacts:
          type: object
          additionalProperties: true
        schools:
          type: array
          items:
            type: object
            properties:
              name:
                $ref: '#/components/schemas/StringOrArray'
              rating:
                type: integer
              distance:
                type: number
              level:
                $ref: '#/components/schemas/StringOrArray'
              type:
                $ref: '#/components/schemas/StringOrArray'
        priceHistory:
          type: array
          items:
            type: object
            properties:
              date:
                $ref: '#/components/schemas/StringOrArray'
              price:
                type: integer
              event:
                $ref: '#/components/schemas/StringOrArray'
        taxHistory:
          type: array
          items:
            type: object
            properties:
              time:
                type: integer
              taxPaid:
                type: number
              value:
                type: integer
    PropertyMinimalInfo:
      type: object
      properties:
        address:
          $ref: '#/components/schemas/Address'
        homeStatus:
          $ref: '#/components/schemas/StringOrArray'
        zestimate:
          type: integer
        bedrooms:
          type: integer
        bathrooms:
          type: number
        livingArea:
          $ref: '#/components/schemas/StringOrArray'
        zpid:
          type: integer
        price:
          type: integer
        yearBuilt:
          type: integer
        daysOnZillow:
          type: integer
        mlsId:
          $ref: '#/components/schemas/StringOrArray'
        propertyZillowURL:
          type: string
        responsivePhotos:
          type: array
          items:
            $ref: '#/components/schemas/ResponsivePhoto'
    ImagesOnly:
      type: object
      properties:
        address:
          $ref: '#/components/schemas/Address'
        zpid:
          type: integer
        responsivePhotos:
          type: array
          items:
            $ref: '#/components/schemas/ResponsivePhoto'
    RentZestimateResponse:
      type: object
      properties:
        data:
          type: object
          properties:
            byAddress:
              type: object
              properties:
                geo:
                  type: object
                  properties:
                    lat:
                      type: number
                    lon:
                      type: number
                floorplans:
                  type: array
                  items:
                    type: object
                    properties:
                      zpid:
                        type: string
                      numBeds:
                        type: integer
                      numFullBaths:
                        type: integer
                      zestimate:
                        type: object
                        properties:
                          rentZestimate:
                            type: integer
                          rentZestimateRangeLow:
                            type: integer
                          rentZestimateRangeHigh:
                            type: integer
                      minSqft:
                        type: integer
                      maxSqft:
                        type: integer
                      address:
                        type: object
                        properties:
                          street:
                            type: string
                          city:
                            type: string
                          state:
                            type: string
                          zip:
                            type: string
                          unit:
                            type: string
                marketSummary:
                  type: object
                  properties:
                    url:
                      type: string
                    areaName:
                      type: string
                    beds:
                      type: array
                      items:
                        type: object
                        properties:
                          summary:
                            type: object
                            properties:
                              medianRent:
                                type: integer
                              monthlyChange:
                                type: number
                              yearlyChange:
                                type: number
                              avgDaysOnMarket:
                                type: integer
                              availableRentals:
                                type: integer
                similarFloorplans:
                  type: object
                  properties:
                    results:
                      type: array
                      items:
                        type: object
                        additionalProperties: true
    SearchResponse:
      type: object
      properties:
        listResults:
          type: array
          items:
            $ref: '#/components/schemas/ListResult'
        mapResults:
          type: array
          items:
            $ref: '#/components/schemas/MapResult'
        totalCount:
          type: integer
    ListResult:
      type: object
      properties:
        zpid:
          type: string
        id:
          type: string
        hasImage:
          type: boolean
        imgSrc:
          type: string
        statusText:
          type: string
        address:
          type: string
        addressStreet:
          type: string
        addressCity:
          type: string
        addressState:
          type: string
        addressZipcode:
          type: string
        area:
          type: integer
        statusType:
          type: string
        detailUrl:
          type: string
        price:
          type: string
        unformattedPrice:
          type: integer
        latLong:
          type: object
          properties:
            latitude:
              type: number
            longitude:
              type: number
        hdpData:
          type: object
          properties:
            homeInfo:
              type: object
              properties:
                zpid:
                  type: integer
                streetAddress:
                  type: string
                city:
                  type: string
                state:
                  type: string
                zipcode:
                  type: string
                price:
                  type: integer
                bathrooms:
                  type: number
                bedrooms:
                  type: integer
                livingArea:
                  type: integer
                homeType:
                  type: string
                homeStatus:
                  type: string
                daysOnZillow:
                  type: integer
                zestimate:
                  type: integer
        carouselPhotos:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
    MapResult:
      type: object
      properties:
        zpid:
          type: string
        address:
          type: string
        price:
          type: string
        beds:
          type: integer
        baths:
          type: number
        area:
          type: integer
        latLong:
          type: object
          properties:
            latitude:
              type: number
            longitude:
              type: number
        statusType:
          type: string
        statusText:
          type: string
tags:
  - name: Health
    description: API health check endpoints
  - name: Property Details
    description: Endpoints for retrieving detailed property information
  - name: Rent Estimate
    description: Endpoints for rental market analysis and estimates
  - name: Property Search
    description: Endpoints for searching properties by various criteria
package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"
    "strconv"
    "propbolt/zestimate"
    "propbolt/details"
    "propbolt/search"
    "propbolt/proxy")

/* internal notes
lsof -i :8080                                    
kill -9
git add .
git commit -m ""
git push origin main
Production: GOOS=linux GOARCH=amd64 go build -o propbolt
Local: go build -o propbolt
Local: PORT=8080 ./propbolt
*/


// Handler for the health check endpoint
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]string{"status": "ok"})
}


// Handler for serving the favicon
func faviconHandler(w http.ResponseWriter, r *http.Request) {
    w.WriteHeader(http.StatusNotFound)
}
// New handler for the /property endpoint
func propertyHandler(w http.ResponseWriter, r *http.Request) {
    proxyConfig := proxy.LoadProxyConfig()
    proxyURL := proxyConfig.GetProxyURL()
    
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    var property details.PropertyInfo
    var err error

    // Check for ID parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }

        property, err = details.FromPropertyID(propertyID, proxyURL)
    } else {
        // Check for URL parameter
        propertyURL := r.URL.Query().Get("url")
        if propertyURL != "" {
            property, err = details.FromPropertyURL(propertyURL, proxyURL)
        } else {
            // Check for Address parameter
            homeAddress := r.URL.Query().Get("address")
            if homeAddress != "" {
                property, err = details.FromHomeAddress(homeAddress, proxyURL)
            } else {
                // If no valid parameters are provided, return an error
                http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
                return
            }
        }
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func rentZestimateHandler(w http.ResponseWriter, r *http.Request) {
    address := r.URL.Query().Get("address")
    if address == "" {
        http.Error(w, "Address parameter is required", http.StatusBadRequest)
        return
    }

    var compPropStatus *bool
    compPropStatusStr := r.URL.Query().Get("compPropStatus")
    if compPropStatusStr != "" {
        if compPropStatusStr == "true" || compPropStatusStr == "false" {
            val, _ := strconv.ParseBool(compPropStatusStr)
            compPropStatus = &val
        } else {
            http.Error(w, "Invalid compPropStatus parameter", http.StatusBadRequest)
            return
        }
    }

    distanceInMilesStr := r.URL.Query().Get("distanceInMiles")
    var distanceInMiles float64 = 5 // Default value
    if distanceInMilesStr != "" {
        var err error
        distanceInMiles, err = strconv.ParseFloat(distanceInMilesStr, 64)
        if err != nil {
            http.Error(w, "Invalid distanceInMiles parameter", http.StatusBadRequest)
            return
        }
    }

    rentZestimate, err := zestimate.GetRentZestimate(address, compPropStatus, distanceInMiles)
    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving rent zestimate: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, rentZestimate)
}

// Handler for testing proxy configuration
func proxyTestHandler(w http.ResponseWriter, r *http.Request) {
    proxyConfig := proxy.LoadProxyConfig()
    
    response := map[string]interface{}{
        "proxy_enabled": proxyConfig.Enabled,
        "proxy_info": proxyConfig.GetProxyInfo(),
    }
    
    // Test proxy connection if enabled
    if proxyConfig.Enabled {
        if err := proxyConfig.TestProxyConnection(); err != nil {
            response["proxy_test"] = "FAILED"
            response["error"] = err.Error()
        } else {
            response["proxy_test"] = "SUCCESS"
        }
    } else {
        response["proxy_test"] = "DISABLED"
    }
    
    writeJSONResponse(w, response)
}
func main() {
    mux := http.NewServeMux()

    mux.HandleFunc("/property", propertyHandler) // New unified endpoint
    mux.HandleFunc("/propertyMinimal", propertyMinimalHandler)
    mux.HandleFunc("/propertyImages", propertyImagesHandler)
    mux.HandleFunc("/rentEstimate", rentZestimateHandler) // New endpoint for property images

    // Search endpoints
    mux.HandleFunc("/search/for-sale", searchForSaleHandler)
    mux.HandleFunc("/search/for-rent", searchForRentHandler)
    mux.HandleFunc("/search/sold", searchSoldHandler)

    // Proxy Test Endpoint
    mux.HandleFunc("/proxy-test", proxyTestHandler)
    // Health Check Endpoint
    mux.HandleFunc("/", healthCheckHandler)
    
    // Favicon handler
    mux.HandleFunc("/favicon.ico", faviconHandler)

    // Get port from environment variable or use default
    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }

    log.Printf("Server starting on port %s", port)
    log.Printf("Proxy configuration: %s", proxy.LoadProxyConfig().GetProxyInfo())
    
    if err := http.ListenAndServe(":"+port, mux); err != nil {
        log.Fatal("Server failed to start:", err)
    }
}

// propertyMinimalHandler returns minimal property information
func propertyMinimalHandler(w http.ResponseWriter, r *http.Request) {
    proxyConfig := proxy.LoadProxyConfig()
    proxyURL := proxyConfig.GetProxyURL()
    
    var property details.PropertyMinimalInfo
    var err error

    // Check for ID parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDMinimal(propertyID, proxyURL)
    } else {
        // Check for URL parameter
        propertyURL := r.URL.Query().Get("url")
        if propertyURL != "" {
            property, err = details.FromPropertyURLMinimal(propertyURL, proxyURL)
        } else {
            // Check for Address parameter
            homeAddress := r.URL.Query().Get("address")
            if homeAddress != "" {
                property, err = details.FromHomeAddressMinimal(homeAddress, proxyURL)
            } else {
                http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
                return
            }
        }
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, property)
}

// propertyImagesHandler returns only property images
func propertyImagesHandler(w http.ResponseWriter, r *http.Request) {
    proxyConfig := proxy.LoadProxyConfig()
    proxyURL := proxyConfig.GetProxyURL()
    
    var images details.ImagesOnly
    var err error

    // Check for ID parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        images, err = details.FromPropertyIDPhotos(propertyID, proxyURL)
    } else {
        // Check for URL parameter
        propertyURL := r.URL.Query().Get("url")
        if propertyURL != "" {
            images, err = details.FromPropertyURLPhotos(propertyURL, proxyURL)
        } else {
            // Check for Address parameter
            homeAddress := r.URL.Query().Get("address")
            if homeAddress != "" {
                images, err = details.FromHomeAddressPhotos(homeAddress, proxyURL)
            } else {
                http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
                return
            }
        }
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property images: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, images)
}

// searchForSaleHandler handles property search for sale
func searchForSaleHandler(w http.ResponseWriter, r *http.Request) {
    proxyConfig := proxy.LoadProxyConfig()
    proxyURL := proxyConfig.GetProxyURL()
    
    // Parse query parameters
    pagination, _ := strconv.Atoi(r.URL.Query().Get("pagination"))
    zoomValue, _ := strconv.Atoi(r.URL.Query().Get("zoom"))
    neLat, _ := strconv.ParseFloat(r.URL.Query().Get("neLat"), 64)
    neLong, _ := strconv.ParseFloat(r.URL.Query().Get("neLong"), 64)
    swLat, _ := strconv.ParseFloat(r.URL.Query().Get("swLat"), 64)
    swLong, _ := strconv.ParseFloat(r.URL.Query().Get("swLong"), 64)
    
    // Parse boolean filters
    isAllHomes, _ := strconv.ParseBool(r.URL.Query().Get("isAllHomes"))
    isElementarySchool, _ := strconv.ParseBool(r.URL.Query().Get("isElementarySchool"))
    isMiddleSchool, _ := strconv.ParseBool(r.URL.Query().Get("isMiddleSchool"))
    isHighSchool, _ := strconv.ParseBool(r.URL.Query().Get("isHighSchool"))
    isPublicSchool, _ := strconv.ParseBool(r.URL.Query().Get("isPublicSchool"))
    isPrivateSchool, _ := strconv.ParseBool(r.URL.Query().Get("isPrivateSchool"))
    isCharterSchool, _ := strconv.ParseBool(r.URL.Query().Get("isCharterSchool"))
    includeUnratedSchools, _ := strconv.ParseBool(r.URL.Query().Get("includeUnratedSchools"))
    isTownhouse, _ := strconv.ParseBool(r.URL.Query().Get("isTownhouse"))
    isMultiFamily, _ := strconv.ParseBool(r.URL.Query().Get("isMultiFamily"))
    isCondo, _ := strconv.ParseBool(r.URL.Query().Get("isCondo"))
    isLotLand, _ := strconv.ParseBool(r.URL.Query().Get("isLotLand"))
    isApartment, _ := strconv.ParseBool(r.URL.Query().Get("isApartment"))
    isManufactured, _ := strconv.ParseBool(r.URL.Query().Get("isManufactured"))
    isApartmentOrCondo, _ := strconv.ParseBool(r.URL.Query().Get("isApartmentOrCondo"))
    
    // Parse price ranges
    priceMin, _ := strconv.Atoi(r.URL.Query().Get("priceMin"))
    priceMax, _ := strconv.Atoi(r.URL.Query().Get("priceMax"))
    monthlyPaymentMin, _ := strconv.Atoi(r.URL.Query().Get("monthlyPaymentMin"))
    monthlyPaymentMax, _ := strconv.Atoi(r.URL.Query().Get("monthlyPaymentMax"))
    
    listResults, mapResults, err := search.ForSale(
        pagination, zoomValue, neLat, neLong, swLat, swLong,
        isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools,
        isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo,
        priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
        proxyURL,
    )
    
    if err != nil {
        http.Error(w, fmt.Sprintf("Error searching properties: %v", err), http.StatusInternalServerError)
        return
    }
    
    writeJSONResponse(w, map[string]interface{}{
        "listResults": listResults,
        "mapResults": mapResults,
    })
}

// searchForRentHandler handles property search for rent
func searchForRentHandler(w http.ResponseWriter, r *http.Request) {
    proxyConfig := proxy.LoadProxyConfig()
    proxyURL := proxyConfig.GetProxyURL()
    
    // Parse query parameters
    pagination, _ := strconv.Atoi(r.URL.Query().Get("pagination"))
    zoomValue, _ := strconv.Atoi(r.URL.Query().Get("zoom"))
    neLat, _ := strconv.ParseFloat(r.URL.Query().Get("neLat"), 64)
    neLong, _ := strconv.ParseFloat(r.URL.Query().Get("neLong"), 64)
    swLat, _ := strconv.ParseFloat(r.URL.Query().Get("swLat"), 64)
    swLong, _ := strconv.ParseFloat(r.URL.Query().Get("swLong"), 64)
    
    listResults, mapResults, err := search.ForRent(pagination, zoomValue, neLat, neLong, swLat, swLong, proxyURL)
    
    if err != nil {
        http.Error(w, fmt.Sprintf("Error searching rental properties: %v", err), http.StatusInternalServerError)
        return
    }
    
    writeJSONResponse(w, map[string]interface{}{
        "listResults": listResults,
        "mapResults": mapResults,
    })
}

// searchSoldHandler handles property search for sold properties
func searchSoldHandler(w http.ResponseWriter, r *http.Request) {
    proxyConfig := proxy.LoadProxyConfig()
    proxyURL := proxyConfig.GetProxyURL()
    
    // Parse query parameters
    pagination, _ := strconv.Atoi(r.URL.Query().Get("pagination"))
    zoomValue, _ := strconv.Atoi(r.URL.Query().Get("zoom"))
    neLat, _ := strconv.ParseFloat(r.URL.Query().Get("neLat"), 64)
    neLong, _ := strconv.ParseFloat(r.URL.Query().Get("neLong"), 64)
    swLat, _ := strconv.ParseFloat(r.URL.Query().Get("swLat"), 64)
    swLong, _ := strconv.ParseFloat(r.URL.Query().Get("swLong"), 64)
    
    listResults, mapResults, err := search.Sold(pagination, zoomValue, neLat, neLong, swLat, swLong, proxyURL)
    
    if err != nil {
        http.Error(w, fmt.Sprintf("Error searching sold properties: %v", err), http.StatusInternalServerError)
        return
    }
    
    writeJSONResponse(w, map[string]interface{}{
        "listResults": listResults,
        "mapResults": mapResults,
    })
}

// writeJSONResponse is a helper function to write JSON responses
func writeJSONResponse(w http.ResponseWriter, data interface{}) {
    w.Header().Set("Content-Type", "application/json")
    if err := json.NewEncoder(w).Encode(data); err != nil {
        http.Error(w, fmt.Sprintf("Error encoding response: %v", err), http.StatusInternalServerError)
    }
}

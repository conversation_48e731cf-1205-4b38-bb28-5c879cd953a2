package search

type SearchRequest struct {
	IsDebugRequest   bool             `json:"isDebugRequest"`
	RequestId        int              `json:"requestId"`
	Wants            Wants            `json:"wants"`
	SearchQueryState SearchQueryState `json:"searchQueryState"`
}

type SearchQueryState struct {
	IsMapVisible  bool       `json:"isMapVisible"`
	IsListVisible bool       `json:"isListVisible"`
	MapZoom       int        `json:"mapZoom"`
	MapBounds     MapBounds  `json:"mapBounds"`
	FilterState   any        `json:"filterState"`
	Pagination    Pagination `json:"pagination"`
}

type MapBounds struct {
	East  float64 `json:"east"`
	North float64 `json:"north"`
	South float64 `json:"south"`
	West  float64 `json:"west"`
}

type FilterInputSale struct {
	SortSelection         JustValueString `json:"sortSelection"`
	IsAllHomes            JustValueBool   `json:"isAllHomes"`
	IsElementarySchool    JustValueBool   `json:"isElementarySchool"`
	IsMiddleSchool        JustValueBool   `json:"isMiddleSchool"`
	IsHighSchool          JustValueBool   `json:"isHighSchool"`
	IsPublicSchool        JustValueBool   `json:"isPublicSchool"`
	IsPrivateSchool       JustValueBool   `json:"isPrivateSchool"`
	IsCharterSchool       JustValueBool   `json:"isCharterSchool"`
	IncludeUnratedSchools JustValueBool   `json:"includeUnratedSchools"`
	IsTownhouse           JustValueBool   `json:"isTownhouse"`
	IsMultiFamily         JustValueBool   `json:"isMultiFamily"`
	IsCondo               JustValueBool   `json:"isCondo"`
	IsLotLand             JustValueBool   `json:"isLotLand"`
	IsApartment           JustValueBool   `json:"isApartment"`
	IsManufactured        JustValueBool   `json:"isManufactured"`
	IsApartmentOrCondo    JustValueBool   `json:"isApartmentOrCondo"`
	Price                 PriceRange      `json:"price"`
	MonthlyPayment        PaymentRange    `json:"monthlyPayment"`
}

type PriceRange struct {
	Max int `json:"max"`
	Min int `json:"min"`
}

type PaymentRange struct {
	Max int `json:"max"`
	Min int `json:"min"`
}

type FilterInputRent struct {
	SortSelection        JustValueString `json:"sortSelection"`
	IsNewConstruction    JustValueBool   `json:"isNewConstruction"`
	IsForSaleForeclosure JustValueBool   `json:"isForSaleForeclosure"`
	IsForSaleByOwner     JustValueBool   `json:"isForSaleByOwner"`
	IsForSaleByAgent     JustValueBool   `json:"isForSaleByAgent"`
	IsForRent            JustValueBool   `json:"isForRent"`
	IsComingSoon         JustValueBool   `json:"isComingSoon"`
	IsAuction            JustValueBool   `json:"isAuction"`
	IsAllHomes           JustValueBool   `json:"isAllHomes"`
}

type FilterInputSold struct {
	SortSelection        JustValueString `json:"sortSelection"`
	IsNewConstruction    JustValueBool   `json:"isNewConstruction"`
	IsForSaleForeclosure JustValueBool   `json:"isForSaleForeclosure"`
	IsForSaleByOwner     JustValueBool   `json:"isForSaleByOwner"`
	IsForSaleByAgent     JustValueBool   `json:"isForSaleByAgent"`
	IsComingSoon         JustValueBool   `json:"isComingSoon"`
	IsAuction            JustValueBool   `json:"isAuction"`
	IsAllHomes           JustValueBool   `json:"isAllHomes"`
	IsRecentlySold       JustValueBool   `json:"isRecentlySold"`
}
type JustValueBool struct {
	Value bool `json:"value"`
}

type JustValueString struct {
	Value string `json:"value"`
}

type Pagination struct {
	CurrentPage int `json:"currentPage"`
}

type Wants struct {
	Cat1 []string `json:"cat1"`
	Cat2 []string `json:"cat2"`
}

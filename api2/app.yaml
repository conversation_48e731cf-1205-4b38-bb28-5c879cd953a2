runtime: go121
service: api2

env_variables:
  PORT: "8080"
  # Proxy Configuration for Residential Proxies
  PROXY_ENABLED: "true"
  PROXY_HOST: "us-wa.proxymesh.com"
  PROXY_PORT: "31280"
  PROXY_USERNAME: "propbolt_user"
  PROXY_PASSWORD: "proxy_pass_2024"
  # Alternative proxy services (uncomment to use)
  # PROXY_HOST: "premium-residential.proxyrack.net"
  # PROXY_PORT: "9000"
  # Rotation settings
  PROXY_ROTATION: "true"
  PROXY_TIMEOUT: "30"
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.65
  target_throughput_utilization: 0.65

handlers:
- url: /.*
  script: auto
  secure: always

health_check:
  enable_health_check: true
  check_interval_sec: 30
  timeout_sec: 4
  unhealthy_threshold: 2
  healthy_threshold: 2
  restart_threshold: 60